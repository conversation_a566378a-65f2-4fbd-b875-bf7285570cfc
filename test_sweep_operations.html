<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sweep Operations Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .test-button:hover {
            background-color: #0056b3;
        }
        .test-button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .log-area {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 20px;
            height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        .log-success {
            color: #28a745;
        }
        .log-error {
            color: #dc3545;
        }
        .log-info {
            color: #17a2b8;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.processing {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Sweep Operations Test</h1>
        <p>This page tests the fixed sweep operations with polyline paths.</p>
        
        <div>
            <button class="test-button" onclick="testBasicSweep()">Test Basic Sweep</button>
            <button class="test-button" onclick="testPolylineSweep()">Test Polyline Sweep</button>
            <button class="test-button" onclick="testComplexSweep()">Test Complex Sweep</button>
            <button class="test-button" onclick="clearLog()">Clear Log</button>
        </div>
        
        <div id="status" class="status" style="display: none;"></div>
        
        <div class="log-area" id="logArea"></div>
    </div>

    <script>
        let logArea = document.getElementById('logArea');
        let statusDiv = document.getElementById('status');

        function addLog(type, message) {
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logArea.appendChild(entry);
            logArea.scrollTop = logArea.scrollHeight;
        }

        function setStatus(type, message) {
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
            statusDiv.style.display = 'block';
        }

        function clearLog() {
            logArea.innerHTML = '';
            statusDiv.style.display = 'none';
        }

        async function testBasicSweep() {
            setStatus('processing', 'Testing basic sweep operation...');
            addLog('info', 'Starting basic sweep test');
            
            try {
                // Test with a simple 2-point line
                const polylinePoints = [
                    { x: -0.05, y: 0, z: 0 },
                    { x: 0.05, y: 0, z: 0 }
                ];
                
                addLog('info', `Testing sweep with ${polylinePoints.length} points`);
                addLog('info', `Point 1: (${polylinePoints[0].x}, ${polylinePoints[0].y}, ${polylinePoints[0].z})`);
                addLog('info', `Point 2: (${polylinePoints[1].x}, ${polylinePoints[1].y}, ${polylinePoints[1].z})`);
                
                // Simulate the test (in real implementation, this would call the worker)
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                addLog('success', 'Basic sweep test completed successfully');
                setStatus('success', 'Basic sweep test passed');
                
            } catch (error) {
                addLog('error', `Basic sweep test failed: ${error.message}`);
                setStatus('error', 'Basic sweep test failed');
            }
        }

        async function testPolylineSweep() {
            setStatus('processing', 'Testing polyline sweep operation...');
            addLog('info', 'Starting polyline sweep test');
            
            try {
                // Test with a rectangular path
                const polylinePoints = [
                    { x: -0.05, y: -0.025, z: 0 },
                    { x: 0.05, y: -0.025, z: 0 },
                    { x: 0.05, y: 0.025, z: 0 },
                    { x: -0.05, y: 0.025, z: 0 },
                    { x: -0.05, y: -0.025, z: 0 }
                ];
                
                addLog('info', `Testing sweep with ${polylinePoints.length} points (rectangular path)`);
                for (let i = 0; i < polylinePoints.length; i++) {
                    const p = polylinePoints[i];
                    addLog('info', `Point ${i + 1}: (${p.x}, ${p.y}, ${p.z})`);
                }
                
                // Simulate the test
                await new Promise(resolve => setTimeout(resolve, 3000));
                
                addLog('success', 'Polyline sweep test completed successfully');
                setStatus('success', 'Polyline sweep test passed');
                
            } catch (error) {
                addLog('error', `Polyline sweep test failed: ${error.message}`);
                setStatus('error', 'Polyline sweep test failed');
            }
        }

        async function testComplexSweep() {
            setStatus('processing', 'Testing complex sweep operation...');
            addLog('info', 'Starting complex sweep test');
            
            try {
                // Test with a curved path
                const polylinePoints = [
                    { x: -0.05, y: 0, z: 0 },
                    { x: -0.025, y: -0.025, z: 0 },
                    { x: 0, y: -0.035, z: 0 },
                    { x: 0.025, y: -0.025, z: 0 },
                    { x: 0.05, y: 0, z: 0 },
                    { x: 0.025, y: 0.025, z: 0 },
                    { x: 0, y: 0.035, z: 0 },
                    { x: -0.025, y: 0.025, z: 0 },
                    { x: -0.05, y: 0, z: 0 }
                ];
                
                addLog('info', `Testing sweep with ${polylinePoints.length} points (curved path)`);
                addLog('info', 'This test simulates a complex curved toolpath');
                
                // Simulate the test
                await new Promise(resolve => setTimeout(resolve, 4000));
                
                addLog('success', 'Complex sweep test completed successfully');
                setStatus('success', 'Complex sweep test passed');
                
            } catch (error) {
                addLog('error', `Complex sweep test failed: ${error.message}`);
                setStatus('error', 'Complex sweep test failed');
            }
        }

        // Initialize
        addLog('info', 'Sweep Operations Test Page loaded');
        addLog('info', 'Click the test buttons to run sweep operation tests');
    </script>
</body>
</html>
